defmodule Drops.Relation.Schema.MetadataExtractorTest do
  use Drops.DataCase, async: false

  alias Drops.Relation.Schema.{MetadataExtractor, PrimaryKey, Indices}
  alias Test.Ecto.TestSchemas

  describe "extract_metadata/2" do
    test "extracts complete metadata from simple schema" do
      metadata = MetadataExtractor.extract_metadata(TestSchemas.UserSchema)

      assert metadata.source == "users"
      assert metadata.primary_key.fields == [:id]
      assert metadata.foreign_keys == []
      # id, name, email, inserted_at, updated_at
      assert length(metadata.fields) == 5
      # No repo provided
      assert Indices.empty?(metadata.indices)
      assert metadata.associations == []
      assert metadata.virtual_fields == []

      # Check field details
      id_field = Enum.find(metadata.fields, &(&1.name == :id))
      assert id_field.type == :integer
      assert id_field.ecto_type == :id

      name_field = Enum.find(metadata.fields, &(&1.name == :name))
      assert name_field.type == :string
      assert name_field.ecto_type == :string
    end

    test "extracts metadata from schema with associations" do
      metadata = MetadataExtractor.extract_metadata(TestSchemas.AssociationsSchema)

      assert metadata.source == "associations"
      assert metadata.primary_key.fields == [:id]
      assert length(metadata.foreign_keys) == 1

      fk = hd(metadata.foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "association_parents"
      assert fk.references_field == :id
      assert fk.association_name == :parent

      assert :items in metadata.associations
      assert :parent in metadata.associations
    end

    test "extracts metadata from schema with composite primary key" do
      metadata = MetadataExtractor.extract_metadata(TestSchemas.CompositePrimaryKeySchema)

      assert metadata.source == "composite_pk"
      assert metadata.primary_key.fields == [:part1, :part2]
      assert PrimaryKey.composite?(metadata.primary_key)
      assert metadata.foreign_keys == []

      # Check composite key fields
      part1_field = Enum.find(metadata.fields, &(&1.name == :part1))
      assert part1_field.type == :string

      part2_field = Enum.find(metadata.fields, &(&1.name == :part2))
      assert part2_field.type == :integer
    end

    test "extracts metadata from schema with no primary key" do
      metadata = MetadataExtractor.extract_metadata(TestSchemas.NoPrimaryKeySchema)

      assert metadata.source == "no_pk"
      assert metadata.primary_key.fields == []
      refute PrimaryKey.present?(metadata.primary_key)
      assert metadata.foreign_keys == []
    end

    test "extracts metadata from schema with custom primary key" do
      metadata = MetadataExtractor.extract_metadata(TestSchemas.CustomPrimaryKeySchema)

      assert metadata.source == "custom_pk"
      assert metadata.primary_key.fields == [:uuid]
      assert PrimaryKey.present?(metadata.primary_key)
      refute PrimaryKey.composite?(metadata.primary_key)

      uuid_field = Enum.find(metadata.fields, &(&1.name == :uuid))
      assert uuid_field.type == :binary
      assert uuid_field.ecto_type == :binary_id
    end

    @tag ecto_schemas: [TestSchemas.UserSchema]
    test "extracts metadata with database indices when repo provided" do
      # Create test table with indices
      Drops.TestRepo.query!("""
      CREATE TABLE IF NOT EXISTS metadata_test_table (
        id INTEGER PRIMARY KEY,
        email TEXT UNIQUE,
        name TEXT
      )
      """)

      Drops.TestRepo.query!(
        "CREATE INDEX IF NOT EXISTS idx_name ON metadata_test_table(name)"
      )

      # Mock a schema module for testing
      defmodule TestMetadataSchema do
        def __schema__(:source), do: "metadata_test_table"
        def __schema__(:primary_key), do: [:id]
        def __schema__(:associations), do: []
        def __schema__(:fields), do: [:id, :email, :name]
        def __schema__(:virtual_fields), do: []
        def __schema__(:type, :id), do: :id
        def __schema__(:type, :email), do: :string
        def __schema__(:type, :name), do: :string
        def __schema__(:field_source, field), do: field
      end

      metadata = MetadataExtractor.extract_metadata(TestMetadataSchema, Drops.TestRepo)

      assert metadata.source == "metadata_test_table"
      refute Indices.empty?(metadata.indices)

      # Should have at least the name index we created
      name_indices = Indices.find_by_field(metadata.indices, :name)
      assert length(name_indices) >= 1

      # Clean up
      Drops.TestRepo.query!("DROP TABLE IF EXISTS metadata_test_table")
    end
  end

  describe "extract_primary_key/1" do
    test "extracts single primary key" do
      pk = MetadataExtractor.extract_primary_key(TestSchemas.UserSchema)

      assert pk.fields == [:id]
      assert PrimaryKey.present?(pk)
      refute PrimaryKey.composite?(pk)
    end

    test "extracts composite primary key" do
      pk = MetadataExtractor.extract_primary_key(TestSchemas.CompositePrimaryKeySchema)

      assert pk.fields == [:part1, :part2]
      assert PrimaryKey.present?(pk)
      assert PrimaryKey.composite?(pk)
    end

    test "handles no primary key" do
      pk = MetadataExtractor.extract_primary_key(TestSchemas.NoPrimaryKeySchema)

      assert pk.fields == []
      refute PrimaryKey.present?(pk)
    end
  end

  describe "extract_foreign_keys/1" do
    test "extracts foreign keys from belongs_to associations" do
      foreign_keys =
        MetadataExtractor.extract_foreign_keys(TestSchemas.AssociationsSchema)

      assert length(foreign_keys) == 1

      fk = hd(foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "association_parents"
      assert fk.references_field == :id
      assert fk.association_name == :parent
    end

    test "extracts multiple foreign keys" do
      foreign_keys =
        MetadataExtractor.extract_foreign_keys(TestSchemas.AssociationItemSchema)

      assert length(foreign_keys) == 1

      fk = hd(foreign_keys)
      assert fk.field == :association_id
      assert fk.references_table == "associations"
      assert fk.association_name == :association
    end

    test "returns empty list for schema without foreign keys" do
      foreign_keys = MetadataExtractor.extract_foreign_keys(TestSchemas.UserSchema)

      assert foreign_keys == []
    end

    test "ignores has_many and has_one associations" do
      # AssociationParentSchema has has_many but no belongs_to
      foreign_keys =
        MetadataExtractor.extract_foreign_keys(TestSchemas.AssociationParentSchema)

      assert foreign_keys == []
    end
  end

  describe "extract_fields/1" do
    test "extracts field metadata with types" do
      fields = MetadataExtractor.extract_fields(TestSchemas.UserSchema)

      assert length(fields) == 5

      id_field = Enum.find(fields, &(&1.name == :id))
      # normalized from :id
      assert id_field.type == :integer
      assert id_field.ecto_type == :id
      assert id_field.source == :id

      name_field = Enum.find(fields, &(&1.name == :name))
      assert name_field.type == :string
      assert name_field.ecto_type == :string

      inserted_at_field = Enum.find(fields, &(&1.name == :inserted_at))
      assert inserted_at_field.type == :naive_datetime
      assert inserted_at_field.ecto_type == :naive_datetime
    end

    test "handles custom primary key types" do
      fields = MetadataExtractor.extract_fields(TestSchemas.CustomPrimaryKeySchema)

      uuid_field = Enum.find(fields, &(&1.name == :uuid))
      # normalized from :binary_id
      assert uuid_field.type == :binary
      assert uuid_field.ecto_type == :binary_id
    end

    test "handles composite primary key fields" do
      fields = MetadataExtractor.extract_fields(TestSchemas.CompositePrimaryKeySchema)

      part1_field = Enum.find(fields, &(&1.name == :part1))
      assert part1_field.type == :string

      part2_field = Enum.find(fields, &(&1.name == :part2))
      assert part2_field.type == :integer
    end
  end

  describe "foreign_key?/2" do
    test "returns true for foreign key fields" do
      foreign_keys =
        MetadataExtractor.extract_foreign_keys(TestSchemas.AssociationsSchema)

      assert MetadataExtractor.foreign_key?(foreign_keys, :parent_id)
      refute MetadataExtractor.foreign_key?(foreign_keys, :name)
    end

    test "returns false for empty foreign key list" do
      refute MetadataExtractor.foreign_key?([], :any_field)
    end
  end

  describe "find_foreign_key/2" do
    test "finds foreign key by field name" do
      foreign_keys =
        MetadataExtractor.extract_foreign_keys(TestSchemas.AssociationsSchema)

      fk = MetadataExtractor.find_foreign_key(foreign_keys, :parent_id)
      assert fk != nil
      assert fk.field == :parent_id

      no_fk = MetadataExtractor.find_foreign_key(foreign_keys, :name)
      assert no_fk == nil
    end
  end
end

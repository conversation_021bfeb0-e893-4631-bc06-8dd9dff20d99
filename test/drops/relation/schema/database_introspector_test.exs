defmodule Drops.Relation.Schema.DatabaseIntrospectorTest do
  use Drops.DataCase, async: false

  alias Drops.Relation.Schema.{DatabaseIntrospector, Indices}

  describe "get_table_indices/2" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "extracts indices from SQLite database" do
      # Create a test table with indices
      Drops.TestRepo.query!("""
      CREATE TABLE IF NOT EXISTS test_indices_table (
        id INTEGER PRIMARY KEY,
        email TEXT UNIQUE,
        name TEXT,
        age INTEGER
      )
      """)

      # Create additional indices
      Drops.TestRepo.query!(
        "CREATE INDEX IF NOT EXISTS idx_name ON test_indices_table(name)"
      )

      Drops.TestRepo.query!(
        "CREATE INDEX IF NOT EXISTS idx_name_age ON test_indices_table(name, age)"
      )

      # Test index extraction
      {:ok, indices} =
        DatabaseIntrospector.get_table_indices(Drops.TestRepo, "test_indices_table")

      assert %Indices{} = indices
      # At least the indices we created
      assert length(indices.indices) >= 2

      # Find specific indices
      name_index = Enum.find(indices.indices, &(&1.name == "idx_name"))
      composite_index = Enum.find(indices.indices, &(&1.name == "idx_name_age"))

      assert name_index != nil
      assert name_index.fields == [:name]
      refute name_index.unique

      assert composite_index != nil
      assert composite_index.fields == [:name, :age]
      refute composite_index.unique

      # Clean up
      Drops.TestRepo.query!("DROP TABLE IF EXISTS test_indices_table")
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "handles table with no custom indices" do
      # Create a simple table without custom indices
      Drops.TestRepo.query!("""
      CREATE TABLE IF NOT EXISTS simple_table (
        id INTEGER PRIMARY KEY,
        data TEXT
      )
      """)

      {:ok, indices} =
        DatabaseIntrospector.get_table_indices(Drops.TestRepo, "simple_table")

      assert %Indices{} = indices
      # Should have minimal or no indices (SQLite may create automatic indices)

      # Clean up
      Drops.TestRepo.query!("DROP TABLE IF EXISTS simple_table")
    end

    test "handles non-existent table" do
      result =
        DatabaseIntrospector.get_table_indices(Drops.TestRepo, "non_existent_table")

      # SQLite doesn't error on non-existent tables for PRAGMA, just returns empty
      case result do
        {:ok, indices} ->
          assert %Indices{} = indices
          assert indices.indices == []

        {:error, _} ->
          # Some adapters might return an error
          assert true
      end
    end
  end

  describe "get_sqlite_indices/2" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "extracts SQLite indices with correct metadata" do
      # Create test table with various index types
      Drops.TestRepo.query!("""
      CREATE TABLE IF NOT EXISTS index_test_table (
        id INTEGER PRIMARY KEY,
        email TEXT,
        username TEXT,
        status TEXT,
        created_at TEXT
      )
      """)

      # Create different types of indices
      Drops.TestRepo.query!(
        "CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_email ON index_test_table(email)"
      )

      Drops.TestRepo.query!(
        "CREATE INDEX IF NOT EXISTS idx_username ON index_test_table(username)"
      )

      Drops.TestRepo.query!(
        "CREATE INDEX IF NOT EXISTS idx_status_created ON index_test_table(status, created_at)"
      )

      {:ok, indices} =
        DatabaseIntrospector.get_sqlite_indices(Drops.TestRepo, "index_test_table")

      assert %Indices{} = indices

      # Find the unique email index
      email_index = Enum.find(indices.indices, &(&1.name == "idx_unique_email"))
      assert email_index != nil
      assert email_index.fields == [:email]
      assert email_index.unique == true
      assert email_index.type == :btree

      # Find the username index
      username_index = Enum.find(indices.indices, &(&1.name == "idx_username"))
      assert username_index != nil
      assert username_index.fields == [:username]
      assert username_index.unique == false

      # Find the composite index
      composite_index = Enum.find(indices.indices, &(&1.name == "idx_status_created"))
      assert composite_index != nil
      assert composite_index.fields == [:status, :created_at]
      assert composite_index.unique == false

      # Clean up
      Drops.TestRepo.query!("DROP TABLE IF EXISTS index_test_table")
    end
  end

  describe "adapter detection" do
    test "detects SQLite adapter correctly" do
      # This is an internal test - we know we're using SQLite in tests
      assert DatabaseIntrospector.get_table_indices(Drops.TestRepo, "users") |> elem(0) ==
               :ok
    end
  end
end

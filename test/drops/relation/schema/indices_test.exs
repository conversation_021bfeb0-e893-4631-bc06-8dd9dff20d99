defmodule Drops.Relation.Schema.IndicesTest do
  use ExUnit.Case, async: true
  
  alias Drops.Relation.Schema.{Index, Indices}
  
  describe "new/1" do
    test "creates empty indices collection" do
      indices = Indices.new()
      
      assert indices.indices == []
      assert Indices.empty?(indices)
    end
    
    test "creates indices collection with provided indices" do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_idx", [:name], false)
      
      indices = Indices.new([index1, index2])
      
      assert length(indices.indices) == 2
      assert index1 in indices.indices
      assert index2 in indices.indices
      refute Indices.empty?(indices)
    end
  end
  
  describe "add_index/2" do
    test "adds index to empty collection" do
      indices = Indices.new()
      index = Index.new("email_idx", [:email], true)
      
      updated_indices = Indices.add_index(indices, index)
      
      assert length(updated_indices.indices) == 1
      assert index in updated_indices.indices
    end
    
    test "adds index to existing collection" do
      index1 = Index.new("email_idx", [:email], true)
      indices = Indices.new([index1])
      
      index2 = Index.new("name_idx", [:name], false)
      updated_indices = Indices.add_index(indices, index2)
      
      assert length(updated_indices.indices) == 2
      assert index1 in updated_indices.indices
      assert index2 in updated_indices.indices
    end
  end
  
  describe "find_by_field/2" do
    setup do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_idx", [:name], false)
      index3 = Index.new("name_email_idx", [:name, :email], false)
      
      indices = Indices.new([index1, index2, index3])
      
      {:ok, indices: indices, index1: index1, index2: index2, index3: index3}
    end
    
    test "finds indices covering a specific field", %{indices: indices, index1: index1, index3: index3} do
      email_indices = Indices.find_by_field(indices, :email)
      
      assert length(email_indices) == 2
      assert index1 in email_indices
      assert index3 in email_indices
    end
    
    test "finds single index covering a field", %{indices: indices, index2: index2} do
      name_indices = Indices.find_by_field(indices, :name)
      
      assert length(name_indices) == 2  # name_idx and name_email_idx
      assert index2 in name_indices
    end
    
    test "returns empty list for field not covered by any index", %{indices: indices} do
      age_indices = Indices.find_by_field(indices, :age)
      
      assert age_indices == []
    end
  end
  
  describe "unique_indices/1" do
    test "finds all unique indices" do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_idx", [:name], false)
      index3 = Index.new("username_idx", [:username], true)
      
      indices = Indices.new([index1, index2, index3])
      
      unique_indices = Indices.unique_indices(indices)
      
      assert length(unique_indices) == 2
      assert index1 in unique_indices
      assert index3 in unique_indices
      refute index2 in unique_indices
    end
    
    test "returns empty list when no unique indices" do
      index1 = Index.new("name_idx", [:name], false)
      index2 = Index.new("email_idx", [:email], false)
      
      indices = Indices.new([index1, index2])
      
      unique_indices = Indices.unique_indices(indices)
      
      assert unique_indices == []
    end
  end
  
  describe "composite_indices/1" do
    test "finds all composite indices" do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_email_idx", [:name, :email], false)
      index3 = Index.new("user_role_idx", [:user_id, :role_id], true)
      
      indices = Indices.new([index1, index2, index3])
      
      composite_indices = Indices.composite_indices(indices)
      
      assert length(composite_indices) == 2
      assert index2 in composite_indices
      assert index3 in composite_indices
      refute index1 in composite_indices
    end
    
    test "returns empty list when no composite indices" do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_idx", [:name], false)
      
      indices = Indices.new([index1, index2])
      
      composite_indices = Indices.composite_indices(indices)
      
      assert composite_indices == []
    end
  end
  
  describe "empty?/1" do
    test "returns true for empty collection" do
      indices = Indices.new()
      
      assert Indices.empty?(indices)
    end
    
    test "returns false for non-empty collection" do
      index = Index.new("email_idx", [:email], true)
      indices = Indices.new([index])
      
      refute Indices.empty?(indices)
    end
  end
  
  describe "count/1" do
    test "returns zero for empty collection" do
      indices = Indices.new()
      
      assert Indices.count(indices) == 0
    end
    
    test "returns correct count for non-empty collection" do
      index1 = Index.new("email_idx", [:email], true)
      index2 = Index.new("name_idx", [:name], false)
      index3 = Index.new("composite_idx", [:name, :email], false)
      
      indices = Indices.new([index1, index2, index3])
      
      assert Indices.count(indices) == 3
    end
  end
end

defmodule Drops.Relation.Schema.IndexTest do
  use ExUnit.Case, async: true
  
  alias Drops.Relation.Schema.Index
  
  describe "new/4" do
    test "creates index with all parameters" do
      index = Index.new("users_email_index", [:email], true, :btree)
      
      assert index.name == "users_email_index"
      assert index.fields == [:email]
      assert index.unique == true
      assert index.type == :btree
    end
    
    test "creates index with defaults" do
      index = Index.new("users_name_index", [:name])
      
      assert index.name == "users_name_index"
      assert index.fields == [:name]
      assert index.unique == false
      assert index.type == nil
    end
    
    test "creates composite index" do
      index = Index.new("users_name_email_index", [:name, :email], false, :btree)
      
      assert index.name == "users_name_email_index"
      assert index.fields == [:name, :email]
      assert index.unique == false
      assert index.type == :btree
    end
  end
  
  describe "composite?/1" do
    test "returns false for single field index" do
      index = Index.new("single_field", [:email], true)
      
      refute Index.composite?(index)
    end
    
    test "returns true for multiple field index" do
      index = Index.new("multi_field", [:name, :email], false)
      
      assert Index.composite?(index)
    end
    
    test "returns false for empty fields" do
      index = Index.new("empty_fields", [], false)
      
      refute Index.composite?(index)
    end
  end
  
  describe "covers_field?/2" do
    test "returns true when index covers the field" do
      index = Index.new("users_email_index", [:email], true)
      
      assert Index.covers_field?(index, :email)
    end
    
    test "returns false when index doesn't cover the field" do
      index = Index.new("users_email_index", [:email], true)
      
      refute Index.covers_field?(index, :name)
    end
    
    test "returns true for composite index covering the field" do
      index = Index.new("users_name_email_index", [:name, :email], false)
      
      assert Index.covers_field?(index, :name)
      assert Index.covers_field?(index, :email)
      refute Index.covers_field?(index, :age)
    end
  end
end

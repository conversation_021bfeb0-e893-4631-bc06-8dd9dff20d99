defmodule Drops.Relation.Schema.PrimaryKeyTest do
  use ExUnit.Case, async: true
  
  alias Drops.Relation.Schema.PrimaryKey
  alias Test.Ecto.TestSchemas
  
  doctest PrimaryKey
  
  describe "new/1" do
    test "creates primary key with single field" do
      pk = PrimaryKey.new([:id])
      
      assert pk.fields == [:id]
    end
    
    test "creates primary key with multiple fields" do
      pk = PrimaryKey.new([:user_id, :role_id])
      
      assert pk.fields == [:user_id, :role_id]
    end
    
    test "creates primary key with no fields" do
      pk = PrimaryKey.new([])
      
      assert pk.fields == []
    end
  end
  
  describe "composite?/1" do
    test "returns false for single field primary key" do
      pk = PrimaryKey.new([:id])
      
      refute PrimaryKey.composite?(pk)
    end
    
    test "returns true for multiple field primary key" do
      pk = PrimaryKey.new([:user_id, :role_id])
      
      assert PrimaryKey.composite?(pk)
    end
    
    test "returns false for no primary key" do
      pk = PrimaryKey.new([])
      
      refute PrimaryKey.composite?(pk)
    end
  end
  
  describe "present?/1" do
    test "returns true when primary key has fields" do
      pk = PrimaryKey.new([:id])
      
      assert PrimaryKey.present?(pk)
    end
    
    test "returns true for composite primary key" do
      pk = PrimaryKey.new([:user_id, :role_id])
      
      assert PrimaryKey.present?(pk)
    end
    
    test "returns false when no primary key" do
      pk = PrimaryKey.new([])
      
      refute PrimaryKey.present?(pk)
    end
  end
  
  describe "from_ecto_schema/1" do
    test "extracts single primary key from Ecto schema" do
      pk = PrimaryKey.from_ecto_schema(TestSchemas.UserSchema)
      
      assert pk.fields == [:id]
      assert PrimaryKey.present?(pk)
      refute PrimaryKey.composite?(pk)
    end
    
    test "extracts composite primary key from Ecto schema" do
      pk = PrimaryKey.from_ecto_schema(TestSchemas.CompositePrimaryKeySchema)
      
      assert pk.fields == [:part1, :part2]
      assert PrimaryKey.present?(pk)
      assert PrimaryKey.composite?(pk)
    end
    
    test "handles schema with no primary key" do
      pk = PrimaryKey.from_ecto_schema(TestSchemas.NoPrimaryKeySchema)
      
      assert pk.fields == []
      refute PrimaryKey.present?(pk)
      refute PrimaryKey.composite?(pk)
    end
    
    test "extracts custom primary key from Ecto schema" do
      pk = PrimaryKey.from_ecto_schema(TestSchemas.CustomPrimaryKeySchema)
      
      assert pk.fields == [:uuid]
      assert PrimaryKey.present?(pk)
      refute PrimaryKey.composite?(pk)
    end
  end
end

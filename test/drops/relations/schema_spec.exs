defmodule Drops.Relations.SchemaSpec do
  use Drops.OperationCase, async: true

  describe "basic schema inference" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "infers basic fields from users table" do
      defmodule Test.Relations.Users do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      assert Test.Relations.Users.__schema__(:fields) == [:id, :name, :email]
      assert Test.Relations.Users.__schema__(:type, :id) == :id
      assert Test.Relations.Users.__schema__(:type, :name) == :string
      assert Test.Relations.Users.__schema__(:type, :email) == :string
    end
  end

  describe "different field types" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.BasicTypesSchema]
    test "infers various field types correctly" do
      defmodule Test.Relations.BasicTypes do
        use Drops.Relation, repo: Drops.TestRepo, name: "basic_types", infer: true
      end

      fields = Test.Relations.BasicTypes.__schema__(:fields)

      # Should include all non-timestamp fields
      assert :id in fields
      assert :string_field in fields
      assert :integer_field in fields
      assert :float_field in fields
      assert :boolean_field in fields
      assert :binary_field in fields
      assert :bitstring_field in fields

      # Check types
      assert Test.Relations.BasicTypes.__schema__(:type, :string_field) == :string
      assert Test.Relations.BasicTypes.__schema__(:type, :integer_field) == :integer
      assert Test.Relations.BasicTypes.__schema__(:type, :float_field) == :float
      assert Test.Relations.BasicTypes.__schema__(:type, :boolean_field) == :boolean
      assert Test.Relations.BasicTypes.__schema__(:type, :binary_field) == :binary
    end
  end

  describe "primary keys" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.CustomPrimaryKeySchema]
    test "handles custom primary keys" do
      defmodule Test.Relations.CustomPK do
        use Drops.Relation, repo: Drops.TestRepo, name: "custom_pk", infer: true
      end

      fields = Test.Relations.CustomPK.__schema__(:fields)

      # Should include the custom primary key and other fields
      assert :uuid in fields
      assert :name in fields

      # The default :id should still be present (Ecto's default behavior)
      assert :id in fields
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.NoPrimaryKeySchema]
    test "handles tables without primary keys" do
      defmodule Test.Relations.NoPK do
        use Drops.Relation, repo: Drops.TestRepo, name: "no_pk", infer: true
      end

      fields = Test.Relations.NoPK.__schema__(:fields)

      # Should include all fields
      # Ecto still adds this
      assert :id in fields
      assert :name in fields
      assert :value in fields
    end
  end

  describe "foreign keys and associations" do
    @tag ecto_schemas: [
           Test.Ecto.TestSchemas.AssociationsSchema,
           Test.Ecto.TestSchemas.AssociationItemSchema,
           Test.Ecto.TestSchemas.AssociationParentSchema
         ]
    test "infers foreign key fields" do
      defmodule Test.Relations.Associations do
        use Drops.Relation, repo: Drops.TestRepo, name: "associations", infer: true
      end

      fields = Test.Relations.Associations.__schema__(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :name in fields
      # belongs_to association
      assert :parent_id in fields

      # Check that foreign key has correct type
      assert Test.Relations.Associations.__schema__(:type, :parent_id) == :id
    end

    @tag ecto_schemas: [
           Test.Ecto.TestSchemas.AssociationsSchema,
           Test.Ecto.TestSchemas.AssociationItemSchema,
           Test.Ecto.TestSchemas.AssociationParentSchema
         ]
    test "infers association item foreign keys" do
      defmodule Test.Relations.AssociationItems do
        use Drops.Relation, repo: Drops.TestRepo, name: "association_items", infer: true
      end

      fields = Test.Relations.AssociationItems.__schema__(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :title in fields
      # belongs_to association
      assert :association_id in fields

      # Check that foreign key has correct type
      assert Test.Relations.AssociationItems.__schema__(:type, :association_id) ==
               :id
    end
  end

  describe "timestamp handling" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.TimestampsSchema]
    test "excludes timestamp fields from inference" do
      defmodule Test.Relations.Timestamps do
        use Drops.Relation, repo: Drops.TestRepo, name: "timestamps", infer: true
      end

      fields = Test.Relations.Timestamps.__schema__(:fields)

      # Should include regular fields but exclude timestamps
      assert :id in fields
      assert :name in fields

      # Timestamps should be excluded from inference
      refute :inserted_at in fields
      refute :updated_at in fields
    end
  end

  describe "constraints and indices" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.ConstraintsSchema]
    test "infers fields with constraints" do
      defmodule Test.Relations.Constraints do
        use Drops.Relation, repo: Drops.TestRepo, name: "constraints", infer: true
      end

      fields = Test.Relations.Constraints.__schema__(:fields)

      # Should include all fields regardless of constraints
      assert :id in fields
      assert :email in fields
      assert :username in fields
      assert :age in fields

      # Check field types
      assert Test.Relations.Constraints.__schema__(:type, :email) == :string
      assert Test.Relations.Constraints.__schema__(:type, :username) == :string
      assert Test.Relations.Constraints.__schema__(:type, :age) == :integer
    end
  end

  describe "error handling" do
    test "handles non-existent table gracefully" do
      # SQLite PRAGMA table_info doesn't error for non-existent tables,
      # it just returns empty results, so the schema will have no fields
      defmodule Test.Relations.NonExistent do
        use Drops.Relation,
          repo: Drops.TestRepo,
          name: "non_existent_table",
          infer: true
      end

      fields = Test.Relations.NonExistent.__schema__(:fields)

      # Should only have the default :id field that Ecto adds
      assert fields == [:id]
    end
  end
end

{application,doctor,
             [{modules,['Elixir.Doctor','Elixir.Doctor.CLI',
                        'Elixir.Doctor.Config','Elixir.Doctor.Docs',
                        'Elixir.Doctor.ModuleInformation',
                        'Elixir.Doctor.ModuleReport',
                        'Elixir.Doctor.ReportUtils','Elixir.Doctor.Reporter',
                        'Elixir.Doctor.Reporters.Full',
                        'Elixir.Doctor.Reporters.ModuleExplain',
                        'Elixir.Doctor.Reporters.OutputUtils',
                        'Elixir.Doctor.Reporters.Short',
                        'Elixir.Doctor.Reporters.Summary',
                        'Elixir.Doctor.Specs','Elixir.Mix.Tasks.Doctor',
                        'Elixir.Mix.Tasks.Doctor.Explain',
                        'Elixir.Mix.Tasks.Doctor.Gen.Config']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,decimal]},
              {description,"Simple utility to create documentation coverage reports"},
              {registered,[]},
              {vsn,"0.21.0"}]}.

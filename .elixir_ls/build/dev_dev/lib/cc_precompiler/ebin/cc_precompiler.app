{application,cc_precompiler,
             [{modules,['Elixir.CCPrecompiler',
                        'Elixir.CCPrecompiler.CompilationScript',
                        'Elixir.CCPrecompiler.UniversalBinary']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,eex]},
              {description,"NIF library Precompiler that uses C/C++ (cross-)compiler."},
              {registered,[]},
              {vsn,"0.1.10"}]}.

{application,makeup_elixir,
             [{modules,['Elixir.Makeup.Lexers.ElixirLexer',
                        'Elixir.Makeup.Lexers.ElixirLexer.Application',
                        'Elixir.Makeup.Lexers.ElixirLexer.Atoms',
                        'Elixir.Makeup.Lexers.ElixirLexer.Helper',
                        'Elixir.Makeup.Lexers.ElixirLexer.Testing',
                        'Elixir.Makeup.Lexers.ElixirLexer.Variables']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,makeup,nimble_parsec]},
              {description,"Elixir lexer for the Makeup syntax highlighter.\n"},
              {registered,[]},
              {vsn,"1.0.1"},
              {mod,{'Elixir.Makeup.Lexers.ElixirLexer.Application',[]}},
              {env,[{sigil_lexers,#{}}]}]}.

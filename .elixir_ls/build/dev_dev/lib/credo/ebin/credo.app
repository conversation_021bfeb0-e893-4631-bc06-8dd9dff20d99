{application,credo,
             [{modules,['Elixir.Credo','Elixir.Credo.Application',
                        'Elixir.Credo.CLI','Elixir.Credo.CLI.Command',
                        'Elixir.Credo.CLI.Command.Categories.CategoriesCommand',
                        'Elixir.Credo.CLI.Command.Categories.CategoriesOutput',
                        'Elixir.Credo.CLI.Command.Categories.Output.Default',
                        'Elixir.Credo.CLI.Command.Categories.Output.Json',
                        'Elixir.Credo.CLI.Command.Diff.DiffCommand',
                        'Elixir.Credo.CLI.Command.Diff.DiffOutput',
                        'Elixir.Credo.CLI.Command.Diff.DiffSummary',
                        'Elixir.Credo.CLI.Command.Diff.Output.Default',
                        'Elixir.Credo.CLI.Command.Diff.Output.FlyCheck',
                        'Elixir.Credo.CLI.Command.Diff.Output.Json',
                        'Elixir.Credo.CLI.Command.Diff.Output.Oneline',
                        'Elixir.Credo.CLI.Command.Diff.Task.FilterIssues',
                        'Elixir.Credo.CLI.Command.Diff.Task.FilterIssuesForExitStatus',
                        'Elixir.Credo.CLI.Command.Diff.Task.GetGitDiff',
                        'Elixir.Credo.CLI.Command.Diff.Task.PrintBeforeInfo',
                        'Elixir.Credo.CLI.Command.Diff.Task.PrintResultsAndSummary',
                        'Elixir.Credo.CLI.Command.Explain.ExplainCommand',
                        'Elixir.Credo.CLI.Command.Explain.ExplainCommand.ExplainCheck',
                        'Elixir.Credo.CLI.Command.Explain.ExplainCommand.ExplainIssue',
                        'Elixir.Credo.CLI.Command.Explain.ExplainCommand.ExplainIssuePreCheck',
                        'Elixir.Credo.CLI.Command.Explain.ExplainOutput',
                        'Elixir.Credo.CLI.Command.Explain.Output.Default',
                        'Elixir.Credo.CLI.Command.Explain.Output.Json',
                        'Elixir.Credo.CLI.Command.GenCheck',
                        'Elixir.Credo.CLI.Command.GenConfig',
                        'Elixir.Credo.CLI.Command.Help',
                        'Elixir.Credo.CLI.Command.Info.InfoCommand',
                        'Elixir.Credo.CLI.Command.Info.InfoCommand.PrintInfo',
                        'Elixir.Credo.CLI.Command.Info.InfoOutput',
                        'Elixir.Credo.CLI.Command.Info.Output.Default',
                        'Elixir.Credo.CLI.Command.Info.Output.Json',
                        'Elixir.Credo.CLI.Command.List.ListCommand',
                        'Elixir.Credo.CLI.Command.List.ListCommand.PrintBeforeInfo',
                        'Elixir.Credo.CLI.Command.List.ListCommand.PrintResultsAndSummary',
                        'Elixir.Credo.CLI.Command.List.ListOutput',
                        'Elixir.Credo.CLI.Command.List.Output.Default',
                        'Elixir.Credo.CLI.Command.List.Output.FlyCheck',
                        'Elixir.Credo.CLI.Command.List.Output.Json',
                        'Elixir.Credo.CLI.Command.List.Output.Oneline',
                        'Elixir.Credo.CLI.Command.List.Output.Sarif',
                        'Elixir.Credo.CLI.Command.Suggest.Output.Default',
                        'Elixir.Credo.CLI.Command.Suggest.Output.FlyCheck',
                        'Elixir.Credo.CLI.Command.Suggest.Output.Json',
                        'Elixir.Credo.CLI.Command.Suggest.Output.Oneline',
                        'Elixir.Credo.CLI.Command.Suggest.Output.Sarif',
                        'Elixir.Credo.CLI.Command.Suggest.SuggestCommand',
                        'Elixir.Credo.CLI.Command.Suggest.SuggestCommand.ManipulateConfigIfRerun',
                        'Elixir.Credo.CLI.Command.Suggest.SuggestCommand.PrintBeforeInfo',
                        'Elixir.Credo.CLI.Command.Suggest.SuggestCommand.PrintResultsAndSummary',
                        'Elixir.Credo.CLI.Command.Suggest.SuggestOutput',
                        'Elixir.Credo.CLI.Command.Version',
                        'Elixir.Credo.CLI.ExitStatus',
                        'Elixir.Credo.CLI.Filename','Elixir.Credo.CLI.Filter',
                        'Elixir.Credo.CLI.Options','Elixir.Credo.CLI.Output',
                        'Elixir.Credo.CLI.Output.FirstRunHint',
                        'Elixir.Credo.CLI.Output.FormatDelegator',
                        'Elixir.Credo.CLI.Output.Formatter.Flycheck',
                        'Elixir.Credo.CLI.Output.Formatter.JSON',
                        'Elixir.Credo.CLI.Output.Formatter.Oneline',
                        'Elixir.Credo.CLI.Output.Formatter.SARIF',
                        'Elixir.Credo.CLI.Output.Shell',
                        'Elixir.Credo.CLI.Output.Summary',
                        'Elixir.Credo.CLI.Output.UI',
                        'Elixir.Credo.CLI.Sorter','Elixir.Credo.CLI.Switch',
                        'Elixir.Credo.CLI.Task.LoadAndValidateSourceFiles',
                        'Elixir.Credo.CLI.Task.PrepareChecksToRun',
                        'Elixir.Credo.CLI.Task.RunChecks',
                        'Elixir.Credo.CLI.Task.SetRelevantIssues',
                        'Elixir.Credo.Check',
                        'Elixir.Credo.Check.ConfigComment',
                        'Elixir.Credo.Check.ConfigCommentFinder',
                        'Elixir.Credo.Check.Consistency.Collector',
                        'Elixir.Credo.Check.Consistency.ExceptionNames',
                        'Elixir.Credo.Check.Consistency.ExceptionNames.Collector',
                        'Elixir.Credo.Check.Consistency.LineEndings',
                        'Elixir.Credo.Check.Consistency.LineEndings.Collector',
                        'Elixir.Credo.Check.Consistency.MultiAliasImportRequireUse',
                        'Elixir.Credo.Check.Consistency.MultiAliasImportRequireUse.Collector',
                        'Elixir.Credo.Check.Consistency.ParameterPatternMatching',
                        'Elixir.Credo.Check.Consistency.ParameterPatternMatching.Collector',
                        'Elixir.Credo.Check.Consistency.SpaceAroundOperators',
                        'Elixir.Credo.Check.Consistency.SpaceAroundOperators.Collector',
                        'Elixir.Credo.Check.Consistency.SpaceAroundOperators.SpaceHelper',
                        'Elixir.Credo.Check.Consistency.SpaceInParentheses',
                        'Elixir.Credo.Check.Consistency.SpaceInParentheses.Collector',
                        'Elixir.Credo.Check.Consistency.TabsOrSpaces',
                        'Elixir.Credo.Check.Consistency.TabsOrSpaces.Collector',
                        'Elixir.Credo.Check.Consistency.UnusedVariableNames',
                        'Elixir.Credo.Check.Consistency.UnusedVariableNames.Collector',
                        'Elixir.Credo.Check.Design.AliasUsage',
                        'Elixir.Credo.Check.Design.DuplicatedCode',
                        'Elixir.Credo.Check.Design.SkipTestWithoutComment',
                        'Elixir.Credo.Check.Design.TagFIXME',
                        'Elixir.Credo.Check.Design.TagHelper',
                        'Elixir.Credo.Check.Design.TagTODO',
                        'Elixir.Credo.Check.Params',
                        'Elixir.Credo.Check.Readability.AliasAs',
                        'Elixir.Credo.Check.Readability.AliasOrder',
                        'Elixir.Credo.Check.Readability.BlockPipe',
                        'Elixir.Credo.Check.Readability.FunctionNames',
                        'Elixir.Credo.Check.Readability.ImplTrue',
                        'Elixir.Credo.Check.Readability.LargeNumbers',
                        'Elixir.Credo.Check.Readability.MaxLineLength',
                        'Elixir.Credo.Check.Readability.ModuleAttributeNames',
                        'Elixir.Credo.Check.Readability.ModuleDoc',
                        'Elixir.Credo.Check.Readability.ModuleNames',
                        'Elixir.Credo.Check.Readability.MultiAlias',
                        'Elixir.Credo.Check.Readability.NestedFunctionCalls',
                        'Elixir.Credo.Check.Readability.NestedFunctionCalls.PipeHelper',
                        'Elixir.Credo.Check.Readability.OneArityFunctionInPipe',
                        'Elixir.Credo.Check.Readability.OnePipePerLine',
                        'Elixir.Credo.Check.Readability.ParenthesesInCondition',
                        'Elixir.Credo.Check.Readability.ParenthesesOnZeroArityDefs',
                        'Elixir.Credo.Check.Readability.PipeIntoAnonymousFunctions',
                        'Elixir.Credo.Check.Readability.PredicateFunctionNames',
                        'Elixir.Credo.Check.Readability.PreferImplicitTry',
                        'Elixir.Credo.Check.Readability.PreferUnquotedAtoms',
                        'Elixir.Credo.Check.Readability.RedundantBlankLines',
                        'Elixir.Credo.Check.Readability.Semicolons',
                        'Elixir.Credo.Check.Readability.SeparateAliasRequire',
                        'Elixir.Credo.Check.Readability.SingleFunctionToBlockPipe',
                        'Elixir.Credo.Check.Readability.SinglePipe',
                        'Elixir.Credo.Check.Readability.SpaceAfterCommas',
                        'Elixir.Credo.Check.Readability.Specs',
                        'Elixir.Credo.Check.Readability.StrictModuleLayout',
                        'Elixir.Credo.Check.Readability.StringSigils',
                        'Elixir.Credo.Check.Readability.TrailingBlankLine',
                        'Elixir.Credo.Check.Readability.TrailingWhiteSpace',
                        'Elixir.Credo.Check.Readability.UnnecessaryAliasExpansion',
                        'Elixir.Credo.Check.Readability.VariableNames',
                        'Elixir.Credo.Check.Readability.WithCustomTaggedTuple',
                        'Elixir.Credo.Check.Readability.WithSingleClause',
                        'Elixir.Credo.Check.Refactor.ABCSize',
                        'Elixir.Credo.Check.Refactor.AppendSingleItem',
                        'Elixir.Credo.Check.Refactor.Apply',
                        'Elixir.Credo.Check.Refactor.CaseTrivialMatches',
                        'Elixir.Credo.Check.Refactor.CondStatements',
                        'Elixir.Credo.Check.Refactor.CyclomaticComplexity',
                        'Elixir.Credo.Check.Refactor.DoubleBooleanNegation',
                        'Elixir.Credo.Check.Refactor.EnumHelpers',
                        'Elixir.Credo.Check.Refactor.FilterCount',
                        'Elixir.Credo.Check.Refactor.FilterFilter',
                        'Elixir.Credo.Check.Refactor.FilterReject',
                        'Elixir.Credo.Check.Refactor.FunctionArity',
                        'Elixir.Credo.Check.Refactor.IoPuts',
                        'Elixir.Credo.Check.Refactor.LongQuoteBlocks',
                        'Elixir.Credo.Check.Refactor.MapInto',
                        'Elixir.Credo.Check.Refactor.MapJoin',
                        'Elixir.Credo.Check.Refactor.MapMap',
                        'Elixir.Credo.Check.Refactor.MatchInCondition',
                        'Elixir.Credo.Check.Refactor.ModuleDependencies',
                        'Elixir.Credo.Check.Refactor.NegatedConditionsInUnless',
                        'Elixir.Credo.Check.Refactor.NegatedConditionsWithElse',
                        'Elixir.Credo.Check.Refactor.NegatedIsNil',
                        'Elixir.Credo.Check.Refactor.Nesting',
                        'Elixir.Credo.Check.Refactor.PassAsyncInTestCases',
                        'Elixir.Credo.Check.Refactor.PerceivedComplexity',
                        'Elixir.Credo.Check.Refactor.PipeChainStart',
                        'Elixir.Credo.Check.Refactor.RedundantWithClauseResult',
                        'Elixir.Credo.Check.Refactor.RejectFilter',
                        'Elixir.Credo.Check.Refactor.RejectReject',
                        'Elixir.Credo.Check.Refactor.UnlessWithElse',
                        'Elixir.Credo.Check.Refactor.UtcNowTruncate',
                        'Elixir.Credo.Check.Refactor.VariableRebinding',
                        'Elixir.Credo.Check.Refactor.WithClauses',
                        'Elixir.Credo.Check.Runner',
                        'Elixir.Credo.Check.Warning.ApplicationConfigInModuleAttribute',
                        'Elixir.Credo.Check.Warning.BoolOperationOnSameValues',
                        'Elixir.Credo.Check.Warning.Dbg',
                        'Elixir.Credo.Check.Warning.ExpensiveEmptyEnumCheck',
                        'Elixir.Credo.Check.Warning.ForbiddenModule',
                        'Elixir.Credo.Check.Warning.IExPry',
                        'Elixir.Credo.Check.Warning.IoInspect',
                        'Elixir.Credo.Check.Warning.LazyLogging',
                        'Elixir.Credo.Check.Warning.LeakyEnvironment',
                        'Elixir.Credo.Check.Warning.MapGetUnsafePass',
                        'Elixir.Credo.Check.Warning.MissedMetadataKeyInLoggerConfig',
                        'Elixir.Credo.Check.Warning.MixEnv',
                        'Elixir.Credo.Check.Warning.OperationOnSameValues',
                        'Elixir.Credo.Check.Warning.OperationWithConstantResult',
                        'Elixir.Credo.Check.Warning.RaiseInsideRescue',
                        'Elixir.Credo.Check.Warning.SpecWithStruct',
                        'Elixir.Credo.Check.Warning.UnsafeExec',
                        'Elixir.Credo.Check.Warning.UnsafeToAtom',
                        'Elixir.Credo.Check.Warning.UnusedEnumOperation',
                        'Elixir.Credo.Check.Warning.UnusedFileOperation',
                        'Elixir.Credo.Check.Warning.UnusedFunctionReturnHelper',
                        'Elixir.Credo.Check.Warning.UnusedKeywordOperation',
                        'Elixir.Credo.Check.Warning.UnusedListOperation',
                        'Elixir.Credo.Check.Warning.UnusedOperation',
                        'Elixir.Credo.Check.Warning.UnusedPathOperation',
                        'Elixir.Credo.Check.Warning.UnusedRegexOperation',
                        'Elixir.Credo.Check.Warning.UnusedStringOperation',
                        'Elixir.Credo.Check.Warning.UnusedTupleOperation',
                        'Elixir.Credo.Check.Warning.WrongTestFileExtension',
                        'Elixir.Credo.Code','Elixir.Credo.Code.Block',
                        'Elixir.Credo.Code.Charlists',
                        'Elixir.Credo.Code.Heredocs',
                        'Elixir.Credo.Code.InterpolationHelper',
                        'Elixir.Credo.Code.Module','Elixir.Credo.Code.Name',
                        'Elixir.Credo.Code.Parameters',
                        'Elixir.Credo.Code.ParserError',
                        'Elixir.Credo.Code.Scope','Elixir.Credo.Code.Sigils',
                        'Elixir.Credo.Code.Strings','Elixir.Credo.Code.Token',
                        'Elixir.Credo.Code.TokenAstCorrelation',
                        'Elixir.Credo.ConfigBuilder',
                        'Elixir.Credo.ConfigFile','Elixir.Credo.Execution',
                        'Elixir.Credo.Execution.ExecutionConfigFiles',
                        'Elixir.Credo.Execution.ExecutionIssues',
                        'Elixir.Credo.Execution.ExecutionSourceFiles',
                        'Elixir.Credo.Execution.ExecutionTiming',
                        'Elixir.Credo.Execution.Task',
                        'Elixir.Credo.Execution.Task.AppendDefaultConfig',
                        'Elixir.Credo.Execution.Task.AppendExtraConfig',
                        'Elixir.Credo.Execution.Task.AssignExitStatusForIssues',
                        'Elixir.Credo.Execution.Task.ConvertCLIOptionsToConfig',
                        'Elixir.Credo.Execution.Task.DetermineCommand',
                        'Elixir.Credo.Execution.Task.InitializeCommand',
                        'Elixir.Credo.Execution.Task.InitializePlugins',
                        'Elixir.Credo.Execution.Task.ParseOptions',
                        'Elixir.Credo.Execution.Task.RequireRequires',
                        'Elixir.Credo.Execution.Task.RunCommand',
                        'Elixir.Credo.Execution.Task.SetDefaultCommand',
                        'Elixir.Credo.Execution.Task.UseColors',
                        'Elixir.Credo.Execution.Task.ValidateConfig',
                        'Elixir.Credo.Execution.Task.ValidateOptions',
                        'Elixir.Credo.Execution.Task.WriteDebugReport',
                        'Elixir.Credo.ExsLoader','Elixir.Credo.Issue',
                        'Elixir.Credo.IssueMeta','Elixir.Credo.Plugin',
                        'Elixir.Credo.Priority',
                        'Elixir.Credo.Service.ConfigFiles',
                        'Elixir.Credo.Service.ETSTableHelper',
                        'Elixir.Credo.Service.SourceFileAST',
                        'Elixir.Credo.Service.SourceFileLines',
                        'Elixir.Credo.Service.SourceFileScopePriorities',
                        'Elixir.Credo.Service.SourceFileScopes',
                        'Elixir.Credo.Service.SourceFileSource',
                        'Elixir.Credo.Severity','Elixir.Credo.SourceFile',
                        'Elixir.Credo.Sources','Elixir.Credo.Test.Assertions',
                        'Elixir.Credo.Test.Case',
                        'Elixir.Credo.Test.CheckRunner',
                        'Elixir.Credo.Test.SourceFiles',
                        'Elixir.Credo.Watcher',
                        'Elixir.Inspect.Credo.SourceFile',
                        'Elixir.Mix.Tasks.Credo',
                        'Elixir.Mix.Tasks.Credo.Gen.Check',
                        'Elixir.Mix.Tasks.Credo.Gen.Config']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,bunt,crypto,eex,ex_unit,
                             file_system,inets,jason,logger]},
              {description,"A static code analysis tool with a focus on code consistency and teaching."},
              {registered,[]},
              {vsn,"1.7.12"},
              {mod,{'Elixir.Credo.Application',[]}}]}.

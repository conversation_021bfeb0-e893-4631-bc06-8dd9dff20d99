{application,ex_doc,
             [{modules,['Elixir.ExDoc','Elixir.ExDoc.Application',
                        'Elixir.ExDoc.Autolink','Elixir.ExDoc.CLI',
                        'Elixir.ExDoc.Config','Elixir.ExDoc.DocAST',
                        'Elixir.ExDoc.DocNode','Elixir.ExDoc.Formatter.EPUB',
                        'Elixir.ExDoc.Formatter.EPUB.Assets',
                        'Elixir.ExDoc.Formatter.EPUB.Templates',
                        'Elixir.ExDoc.Formatter.HTML',
                        'Elixir.ExDoc.Formatter.HTML.Assets',
                        'Elixir.ExDoc.Formatter.HTML.SearchData',
                        'Elixir.ExDoc.Formatter.HTML.Templates',
                        'Elixir.ExDoc.GroupMatcher','Elixir.ExDoc.Language',
                        'Elixir.ExDoc.Language.Elixir',
                        'Elixir.ExDoc.Language.Erlang',
                        'Elixir.ExDoc.Language.Source',
                        'Elixir.ExDoc.Markdown',
                        'Elixir.ExDoc.Markdown.Earmark',
                        'Elixir.ExDoc.ModuleNode','Elixir.ExDoc.Refs',
                        'Elixir.ExDoc.Retriever',
                        'Elixir.ExDoc.Retriever.Error',
                        'Elixir.ExDoc.ShellLexer','Elixir.ExDoc.Utils',
                        'Elixir.Mix.Tasks.Docs']},
              {optional_applications,[makeup_c,makeup_html]},
              {applications,[kernel,stdlib,elixir,eex,earmark_parser,
                             makeup_elixir,makeup_erlang,makeup_c,
                             makeup_html]},
              {description,"ExDoc is a documentation generation tool for Elixir"},
              {registered,[]},
              {vsn,"0.38.2"},
              {mod,{'Elixir.ExDoc.Application',[]}}]}.

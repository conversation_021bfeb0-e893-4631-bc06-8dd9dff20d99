{application,nimble_parsec,
             [{modules,['Elixir.Mix.Tasks.NimbleParsec.Compile',
                        'Elixir.NimbleParsec','Elixir.NimbleParsec.Compiler',
                        'Elixir.NimbleParsec.Recorder']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"A simple and fast library for text-based parser combinators"},
              {registered,[]},
              {vsn,"1.4.2"}]}.

{application,earmark_parser,
             [{modules,['Elixir.EarmarkParser',
                        'Elixir.EarmarkParser.Ast.Emitter',
                        'Elixir.EarmarkParser.Ast.Inline',
                        'Elixir.EarmarkParser.Ast.Renderer.AstWalker',
                        'Elixir.EarmarkParser.Ast.Renderer.FootnoteRenderer',
                        'Elixir.EarmarkParser.Ast.Renderer.HtmlRenderer',
                        'Elixir.EarmarkParser.Ast.Renderer.TableRenderer',
                        'Elixir.EarmarkParser.AstRenderer',
                        'Elixir.EarmarkParser.Block.BlockQuote',
                        'Elixir.EarmarkParser.Block.Code',
                        'Elixir.EarmarkParser.Block.FnDef',
                        'Elixir.EarmarkParser.Block.FnList',
                        'Elixir.EarmarkParser.Block.Heading',
                        'Elixir.EarmarkParser.Block.Html',
                        'Elixir.EarmarkParser.Block.HtmlComment',
                        'Elixir.EarmarkParser.Block.HtmlOneline',
                        'Elixir.EarmarkParser.Block.Ial',
                        'Elixir.EarmarkParser.Block.IdDef',
                        'Elixir.EarmarkParser.Block.List',
                        'Elixir.EarmarkParser.Block.ListItem',
                        'Elixir.EarmarkParser.Block.Para',
                        'Elixir.EarmarkParser.Block.Ruler',
                        'Elixir.EarmarkParser.Block.Table',
                        'Elixir.EarmarkParser.Block.Text',
                        'Elixir.EarmarkParser.Context',
                        'Elixir.EarmarkParser.Enum.Ext',
                        'Elixir.EarmarkParser.Helpers',
                        'Elixir.EarmarkParser.Helpers.AstHelpers',
                        'Elixir.EarmarkParser.Helpers.AttrParser',
                        'Elixir.EarmarkParser.Helpers.HtmlParser',
                        'Elixir.EarmarkParser.Helpers.LeexHelpers',
                        'Elixir.EarmarkParser.Helpers.LineHelpers',
                        'Elixir.EarmarkParser.Helpers.LookaheadHelpers',
                        'Elixir.EarmarkParser.Helpers.PureLinkHelpers',
                        'Elixir.EarmarkParser.Helpers.ReparseHelpers',
                        'Elixir.EarmarkParser.Helpers.StringHelpers',
                        'Elixir.EarmarkParser.Helpers.YeccHelpers',
                        'Elixir.EarmarkParser.Line',
                        'Elixir.EarmarkParser.Line.Blank',
                        'Elixir.EarmarkParser.Line.BlockQuote',
                        'Elixir.EarmarkParser.Line.Fence',
                        'Elixir.EarmarkParser.Line.FnDef',
                        'Elixir.EarmarkParser.Line.Heading',
                        'Elixir.EarmarkParser.Line.HtmlCloseTag',
                        'Elixir.EarmarkParser.Line.HtmlComment',
                        'Elixir.EarmarkParser.Line.HtmlOneLine',
                        'Elixir.EarmarkParser.Line.HtmlOpenTag',
                        'Elixir.EarmarkParser.Line.Ial',
                        'Elixir.EarmarkParser.Line.IdDef',
                        'Elixir.EarmarkParser.Line.Indent',
                        'Elixir.EarmarkParser.Line.ListItem',
                        'Elixir.EarmarkParser.Line.Ruler',
                        'Elixir.EarmarkParser.Line.SetextUnderlineHeading',
                        'Elixir.EarmarkParser.Line.TableLine',
                        'Elixir.EarmarkParser.Line.Text',
                        'Elixir.EarmarkParser.LineScanner',
                        'Elixir.EarmarkParser.LineScanner.Rgx',
                        'Elixir.EarmarkParser.Message',
                        'Elixir.EarmarkParser.Options',
                        'Elixir.EarmarkParser.Parser',
                        'Elixir.EarmarkParser.Parser.FootnoteParser',
                        'Elixir.EarmarkParser.Parser.LinkParser',
                        'Elixir.EarmarkParser.Parser.ListInfo',
                        'Elixir.EarmarkParser.Parser.ListParser',
                        earmark_parser_link_text_lexer,
                        earmark_parser_link_text_parser,
                        earmark_parser_string_lexer]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"AST parser and generator for Markdown"},
              {registered,[]},
              {vsn,"1.4.44"}]}.

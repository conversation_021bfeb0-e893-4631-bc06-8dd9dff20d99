defmodule Drops.Relation do
  defmacro __using__(opts) do
    quote do
      use Ecto.Schema

      import Drops.Relation

      @before_compile Drops.Relation

      @opts unquote(opts)
    end
  end

  defmacro __before_compile__(env) do
    relation = env.module

    opts = Module.get_attribute(relation, :opts)
    repo = opts[:repo]
    name = opts[:name]

    schema = Drops.Relation.Inference.infer_schema(relation, name, repo)

    quote do
      require unquote(repo)

      unquote(schema)
    end
  end

  defmodule Inference do
    def infer_schema(_relation, name, repo) do
      # Introspect table columns and types using SQLite PRAGMA
      columns = introspect_table_columns(repo, name)

      # Generate Ecto schema fields from column information
      field_definitions = generate_field_definitions(columns)

      quote do
        schema unquote(name) do
          (unquote_splicing(field_definitions))
        end
      end
    end

    defp introspect_table_columns(repo, table_name) do
      # Use SQLite PRAGMA table_info to get column information
      query = "PRAGMA table_info(#{table_name})"

      case repo.query(query) do
        {:ok, %{rows: rows, columns: _columns}} ->
          # PRAGMA table_info returns: [cid, name, type, notnull, dflt_value, pk]
          Enum.map(rows, fn [_cid, name, type, notnull, _dflt_value, pk] ->
            %{
              name: name,
              type: type,
              not_null: notnull == 1,
              primary_key: pk == 1
            }
          end)

        {:error, error} ->
          raise "Failed to introspect table #{table_name}: #{inspect(error)}"
      end
    end

    defp generate_field_definitions(columns) do
      columns
      |> Enum.reject(fn column ->
        # Skip timestamp fields and primary key named 'id' (Ecto adds this automatically)
        column.name in ["inserted_at", "updated_at"] or
          (column.primary_key and column.name == "id")
      end)
      |> Enum.map(fn column ->
        field_name = String.to_atom(column.name)
        ecto_type = sqlite_type_to_ecto_type(column.type, field_name)

        # Regular field (primary key 'id' is handled automatically by Ecto)
        quote do
          field(unquote(field_name), unquote(ecto_type))
        end
      end)
    end

    defp sqlite_type_to_ecto_type(sqlite_type, field_name) do
      case String.upcase(sqlite_type) do
        "INTEGER" ->
          # If field name ends with _id, it's likely a foreign key
          if field_name && String.ends_with?(Atom.to_string(field_name), "_id") do
            :id
          else
            :integer
          end

        "TEXT" ->
          :string

        "REAL" ->
          :float

        "BLOB" ->
          :binary

        "DATETIME" ->
          :naive_datetime

        "DATE" ->
          :date

        "TIME" ->
          :time

        "BOOLEAN" ->
          :boolean

        "JSON" ->
          :map

        # Default to string for unknown types
        _ ->
          :string
      end
    end
  end
end

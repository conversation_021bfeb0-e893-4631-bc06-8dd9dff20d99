defmodule Drops.Relation.Schema.Index do
  @moduledoc """
  Represents a database index on a table.
  
  This struct stores information about a database index including its name,
  the fields it covers, whether it's unique, and its type.
  
  ## Examples
  
      # Simple index
      %Drops.Relation.Schema.Index{
        name: "users_email_index",
        fields: [:email],
        unique: true,
        type: :btree
      }
      
      # Composite index
      %Drops.Relation.Schema.Index{
        name: "users_name_email_index", 
        fields: [:name, :email],
        unique: false,
        type: :btree
      }
  """
  
  @type index_type :: :btree | :hash | :gin | :gist | :brin | nil
  
  @type t :: %__MODULE__{
    name: String.t(),
    fields: [atom()],
    unique: boolean(),
    type: index_type()
  }
  
  defstruct [:name, :fields, :unique, :type]
  
  @doc """
  Creates a new Index struct.
  
  ## Parameters
  
  - `name` - The index name
  - `fields` - List of field names covered by the index
  - `unique` - Whether the index enforces uniqueness
  - `type` - The index type (optional)
  
  ## Examples
  
      iex> Drops.Relation.Schema.Index.new("users_email_index", [:email], true, :btree)
      %Drops.Relation.Schema.Index{
        name: "users_email_index",
        fields: [:email],
        unique: true,
        type: :btree
      }
  """
  @spec new(String.t(), [atom()], boolean(), index_type()) :: t()
  def new(name, fields, unique \\ false, type \\ nil) do
    %__MODULE__{
      name: name,
      fields: fields,
      unique: unique,
      type: type
    }
  end
  
  @doc """
  Checks if the index is composite (covers multiple fields).
  
  ## Examples
  
      iex> index = Drops.Relation.Schema.Index.new("single_field", [:email], true)
      iex> Drops.Relation.Schema.Index.composite?(index)
      false
      
      iex> index = Drops.Relation.Schema.Index.new("multi_field", [:name, :email], false)
      iex> Drops.Relation.Schema.Index.composite?(index)
      true
  """
  @spec composite?(t()) :: boolean()
  def composite?(%__MODULE__{fields: fields}) do
    length(fields) > 1
  end
  
  @doc """
  Checks if the index covers a specific field.
  
  ## Examples
  
      iex> index = Drops.Relation.Schema.Index.new("users_email_index", [:email], true)
      iex> Drops.Relation.Schema.Index.covers_field?(index, :email)
      true
      
      iex> Drops.Relation.Schema.Index.covers_field?(index, :name)
      false
  """
  @spec covers_field?(t(), atom()) :: boolean()
  def covers_field?(%__MODULE__{fields: fields}, field) do
    field in fields
  end
end

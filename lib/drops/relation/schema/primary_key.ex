defmodule Drops.Relation.Schema.PrimaryKey do
  @moduledoc """
  Represents primary key information for a database table/schema.

  This struct stores information about primary key fields, supporting both
  single-column and composite primary keys.

  ## Examples

      # Single primary key
      %Drops.Relation.Schema.PrimaryKey{fields: [:id]}

      # Composite primary key
      %Drops.Relation.Schema.PrimaryKey{fields: [:user_id, :role_id]}

      # No primary key
      %Drops.Relation.Schema.PrimaryKey{fields: []}
  """

  @type t :: %__MODULE__{
          fields: [atom()]
        }

  defstruct [:fields]

  @doc """
  Creates a new PrimaryKey struct.

  ## Parameters

  - `fields` - List of field names that form the primary key

  ## Examples

      iex> Drops.Relation.Schema.PrimaryKey.new([:id])
      %Drops.Relation.Schema.PrimaryKey{fields: [:id]}

      iex> Drops.Relation.Schema.PrimaryKey.new([:user_id, :role_id])
      %Drops.Relation.Schema.PrimaryKey{fields: [:user_id, :role_id]}
  """
  @spec new([atom()]) :: t()
  def new(fields) when is_list(fields) do
    %__MODULE__{fields: fields}
  end

  @doc """
  Checks if the primary key is composite (has multiple fields).

  ## Examples

      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:id])
      iex> Drops.Relation.Schema.PrimaryKey.composite?(pk)
      false

      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:user_id, :role_id])
      iex> Drops.Relation.Schema.PrimaryKey.composite?(pk)
      true
  """
  @spec composite?(t()) :: boolean()
  def composite?(%__MODULE__{fields: fields}) do
    length(fields) > 1
  end

  @doc """
  Checks if the schema has a primary key.

  ## Examples

      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:id])
      iex> Drops.Relation.Schema.PrimaryKey.present?(pk)
      true

      iex> pk = Drops.Relation.Schema.PrimaryKey.new([])
      iex> Drops.Relation.Schema.PrimaryKey.present?(pk)
      false
  """
  @spec present?(t()) :: boolean()
  def present?(%__MODULE__{fields: fields}) do
    fields != []
  end

  @doc """
  Extracts primary key information from an Ecto schema module.

  ## Parameters

  - `schema_module` - An Ecto schema module

  ## Examples

      iex> # Example with a hypothetical schema
      iex> # Drops.Relation.Schema.PrimaryKey.from_ecto_schema(MyApp.User)
      iex> # %Drops.Relation.Schema.PrimaryKey{fields: [:id]}
      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:id])
      iex> pk.fields
      [:id]
  """
  @spec from_ecto_schema(module()) :: t()
  def from_ecto_schema(schema_module) when is_atom(schema_module) do
    fields = schema_module.__schema__(:primary_key)
    new(fields)
  end
end

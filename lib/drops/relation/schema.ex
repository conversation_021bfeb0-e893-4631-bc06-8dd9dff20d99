defmodule Drops.Relation.Schema do
  @moduledoc """
  Represents comprehensive schema metadata for a database table/relation.
  
  This struct stores all extracted metadata about a database table including
  primary keys, foreign keys, field information, and indices. It serves as
  a central container for schema information that can be used for validation,
  documentation, and code generation.
  
  ## Examples
  
      # Create a schema with metadata
      schema = %Drops.Relation.Schema{
        source: "users",
        primary_key: %Drops.Relation.Schema.PrimaryKey{fields: [:id]},
        foreign_keys: [],
        fields: [
          %{name: :id, type: :integer, ecto_type: :id, source: :id},
          %{name: :email, type: :string, ecto_type: :string, source: :email}
        ],
        indices: %Drops.Relation.Schema.Indices{indices: [...]},
        associations: [],
        virtual_fields: []
      }
  """
  
  alias Drops.Relation.Schema.{PrimaryKey, ForeignKey, Indices}
  
  @type field_metadata :: %{
    name: atom(),
    type: atom(),
    ecto_type: term(),
    source: atom()
  }
  
  @type t :: %__MODULE__{
    source: String.t(),
    primary_key: PrimaryKey.t(),
    foreign_keys: [ForeignKey.t()],
    fields: [field_metadata()],
    indices: Indices.t(),
    associations: [atom()],
    virtual_fields: [atom()]
  }
  
  defstruct [
    :source,
    :primary_key,
    :foreign_keys,
    :fields,
    :indices,
    :associations,
    :virtual_fields
  ]
  
  @doc """
  Creates a new Schema struct with the provided metadata.
  
  ## Parameters
  
  - `source` - The table name
  - `primary_key` - Primary key information
  - `foreign_keys` - List of foreign key relationships
  - `fields` - List of field metadata
  - `indices` - Index information
  - `associations` - List of association names
  - `virtual_fields` - List of virtual field names
  
  ## Examples
  
      iex> pk = Drops.Relation.Schema.PrimaryKey.new([:id])
      iex> indices = Drops.Relation.Schema.Indices.new([])
      iex> schema = Drops.Relation.Schema.new("users", pk, [], [], indices, [], [])
      iex> schema.source
      "users"
  """
  @spec new(String.t(), PrimaryKey.t(), [ForeignKey.t()], [field_metadata()], Indices.t(), [atom()], [atom()]) :: t()
  def new(source, primary_key, foreign_keys, fields, indices, associations, virtual_fields) do
    %__MODULE__{
      source: source,
      primary_key: primary_key,
      foreign_keys: foreign_keys,
      fields: fields,
      indices: indices,
      associations: associations,
      virtual_fields: virtual_fields
    }
  end
  
  @doc """
  Creates a Schema struct from an Ecto schema module.
  
  This function uses the MetadataExtractor to gather all available metadata
  from the Ecto schema and optionally from the database.
  
  ## Parameters
  
  - `schema_module` - The Ecto schema module
  - `repo` - The Ecto repository (optional, required for index introspection)
  
  ## Examples
  
      iex> schema = Drops.Relation.Schema.from_ecto_schema(MyApp.User, MyApp.Repo)
      iex> schema.source
      "users"
  """
  @spec from_ecto_schema(module(), module() | nil) :: t()
  def from_ecto_schema(schema_module, repo \\ nil) when is_atom(schema_module) do
    alias Drops.Relation.Schema.MetadataExtractor
    
    metadata = MetadataExtractor.extract_metadata(schema_module, repo)
    
    %__MODULE__{
      source: metadata.source,
      primary_key: metadata.primary_key,
      foreign_keys: metadata.foreign_keys,
      fields: metadata.fields,
      indices: metadata.indices,
      associations: metadata.associations,
      virtual_fields: metadata.virtual_fields
    }
  end
  
  @doc """
  Finds a field by name in the schema.
  
  ## Examples
  
      iex> field = Drops.Relation.Schema.find_field(schema, :email)
      iex> field.name
      :email
  """
  @spec find_field(t(), atom()) :: field_metadata() | nil
  def find_field(%__MODULE__{fields: fields}, field_name) when is_atom(field_name) do
    Enum.find(fields, & &1.name == field_name)
  end
  
  @doc """
  Checks if a field is a primary key field.
  
  ## Examples
  
      iex> Drops.Relation.Schema.primary_key_field?(schema, :id)
      true
  """
  @spec primary_key_field?(t(), atom()) :: boolean()
  def primary_key_field?(%__MODULE__{primary_key: primary_key}, field_name) when is_atom(field_name) do
    field_name in primary_key.fields
  end
  
  @doc """
  Checks if a field is a foreign key field.
  
  ## Examples
  
      iex> Drops.Relation.Schema.foreign_key_field?(schema, :user_id)
      true
  """
  @spec foreign_key_field?(t(), atom()) :: boolean()
  def foreign_key_field?(%__MODULE__{foreign_keys: foreign_keys}, field_name) when is_atom(field_name) do
    Enum.any?(foreign_keys, & &1.field == field_name)
  end
  
  @doc """
  Gets the foreign key information for a specific field.
  
  ## Examples
  
      iex> fk = Drops.Relation.Schema.get_foreign_key(schema, :user_id)
      iex> fk.references_table
      "users"
  """
  @spec get_foreign_key(t(), atom()) :: ForeignKey.t() | nil
  def get_foreign_key(%__MODULE__{foreign_keys: foreign_keys}, field_name) when is_atom(field_name) do
    Enum.find(foreign_keys, & &1.field == field_name)
  end
  
  @doc """
  Checks if the schema has a composite primary key.
  
  ## Examples
  
      iex> Drops.Relation.Schema.composite_primary_key?(schema)
      false
  """
  @spec composite_primary_key?(t()) :: boolean()
  def composite_primary_key?(%__MODULE__{primary_key: primary_key}) do
    PrimaryKey.composite?(primary_key)
  end
  
  @doc """
  Gets all field names in the schema.
  
  ## Examples
  
      iex> Drops.Relation.Schema.field_names(schema)
      [:id, :name, :email, :created_at, :updated_at]
  """
  @spec field_names(t()) :: [atom()]
  def field_names(%__MODULE__{fields: fields}) do
    Enum.map(fields, & &1.name)
  end
  
  @doc """
  Gets all foreign key field names in the schema.
  
  ## Examples
  
      iex> Drops.Relation.Schema.foreign_key_field_names(schema)
      [:user_id, :category_id]
  """
  @spec foreign_key_field_names(t()) :: [atom()]
  def foreign_key_field_names(%__MODULE__{foreign_keys: foreign_keys}) do
    Enum.map(foreign_keys, & &1.field)
  end
end
